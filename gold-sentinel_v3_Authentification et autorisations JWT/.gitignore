# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Environment variables
.env
.env.local
.env.production
.env.staging

# Virtual environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (Frontend)
frontend/node_modules/
frontend/dist/
frontend/build/
frontend/.env
frontend/.env.local
frontend/.env.production
frontend/.env.staging
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Secrets et clés
secrets/
*.key
*.pem
*.p12
gee-service-account-key.json

# Modèles AI (trop volumineux)
ai/models/*.h5
ai/models/*.pkl
ai/models/*.joblib

# Logs
logs/
*.log

# Cache
.cache/
.pytest_cache/

# Coverage
htmlcov/
.coverage
.coverage.*
coverage.xml

# Base de données locale
*.db
*.sqlite
*.sqlite3

# Fichiers temporaires
tmp/
temp/
.tmp/

# Docker
.dockerignore
docker-compose.override.yml