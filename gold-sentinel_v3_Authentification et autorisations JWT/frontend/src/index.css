@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

/* Reset et base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Couleurs principales GoldSentinel */
  --gold: #FFD700;
  --night-blue: #0B1E3F;
  --forest-green: #1C6B48;
  --alert-red: #E63946;
  --pure-white: #FFFFFF;

  /* Variations pour les états */
  --gold-light: #FFF4A3;
  --gold-dark: #B8860B;
  --night-blue-light: #1A2B4C;
  --night-blue-dark: #051122;
  --forest-green-light: #2A8B5F;
  --forest-green-dark: #0F4A2F;
}

body {
  font-family: 'Open Sans', 'Roboto', sans-serif;
  background-color: var(--pure-white);
  color: var(--night-blue);
  min-height: 100vh;
  font-size: 14px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', 'Poppins', sans-serif;
  font-weight: 600;
  color: var(--night-blue);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* Boutons principaux */
.btn-primary {
  background-color: var(--gold);
  color: var(--night-blue);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 14px 0 rgba(255, 215, 0, 0.39);
}

.btn-primary:hover {
  background-color: var(--gold-dark);
  box-shadow: 0 6px 20px 0 rgba(255, 215, 0, 0.5);
}

.btn-secondary {
  background-color: var(--night-blue);
  color: var(--pure-white);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 14px 0 rgba(11, 30, 63, 0.39);
}

.btn-secondary:hover {
  background-color: var(--night-blue-light);
  box-shadow: 0 6px 20px 0 rgba(11, 30, 63, 0.5);
}

.btn-success {
  background-color: var(--forest-green);
  color: var(--pure-white);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 14px 0 rgba(28, 107, 72, 0.39);
}

.btn-success:hover {
  background-color: var(--forest-green-light);
  box-shadow: 0 6px 20px 0 rgba(28, 107, 72, 0.5);
}

.btn-danger {
  background-color: var(--alert-red);
  color: var(--pure-white);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-danger:hover {
  background-color: #c53030;
  box-shadow: 0 6px 20px 0 rgba(230, 57, 70, 0.5);
}

.btn-outline {
  border: 2px solid var(--gold);
  color: var(--gold);
  background-color: transparent;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-outline:hover {
  background-color: var(--gold);
  color: var(--night-blue);
}

/* Cards */
.card {
  background-color: var(--pure-white);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.card-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

/* Formulaires */
.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.2s;
}

.form-input:focus {
  border-color: var(--gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--night-blue);
  margin-bottom: 0.5rem;
}

.form-error {
  color: var(--alert-red);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Alertes */
.alert {
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid;
}

.alert-success {
  background-color: #f0fdf4;
  border-color: var(--forest-green);
  color: var(--forest-green-dark);
}

.alert-warning {
  background-color: #fffbeb;
  border-color: var(--gold);
  color: #92400e;
}

.alert-error {
  background-color: #fef2f2;
  border-color: var(--alert-red);
  color: #991b1b;
}

.alert-info {
  background-color: #eff6ff;
  border-color: var(--night-blue);
  color: var(--night-blue);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-primary {
  background-color: var(--gold);
  color: var(--night-blue);
}

.badge-secondary {
  background-color: var(--night-blue);
  color: var(--pure-white);
}

.badge-success {
  background-color: var(--forest-green);
  color: var(--pure-white);
}

.badge-danger {
  background-color: var(--alert-red);
  color: var(--pure-white);
}

/* Sidebar */
.sidebar {
  background-color: var(--night-blue);
  color: var(--pure-white);
  min-height: 100vh;
  width: 16rem;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 40;
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.sidebar.open {
  transform: translateX(0);
}

@media (min-width: 1024px) {
  .sidebar {
    transform: translateX(0);
  }
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s;
}

.sidebar-item:hover {
  background-color: var(--night-blue-light);
  color: var(--gold);
}

.sidebar-item-active {
  background-color: var(--night-blue-light);
  color: var(--gold);
  border-right: 4px solid var(--gold);
}

/* Loading */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border: 2px solid transparent;
  border-bottom-color: var(--gold);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive utilities */
.container-app {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-app {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-app {
    padding: 0 2rem;
  }
}

/* Gradients */
.bg-gradient-gold {
  background: linear-gradient(135deg, #FFD700 0%, #B8860B 100%);
}

.bg-gradient-night {
  background: linear-gradient(135deg, #0B1E3F 0%, #051122 100%);
}

.bg-gradient-forest {
  background: linear-gradient(135deg, #1C6B48 0%, #0F4A2F 100%);
}

/* Layout */
.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.text-center {
  text-align: center;
}

.hidden {
  display: none;
}

@media (min-width: 1024px) {
  .lg\\:ml-64 {
    margin-left: 16rem;
  }

  .lg\\:hidden {
    display: none;
  }
}

/* Utilities supplémentaires */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Page de login - Force le centrage complet */
.login-page {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, #0B1E3F 0%, #051122 100%) !important;
}

.login-container {
  width: 100% !important;
  max-width: 28rem !important;
  padding: 3rem 1rem !important;
  margin: 0 auto !important;
}

/* Classes utilitaires manquantes */
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-10 { width: 2.5rem; }
.h-10 { height: 2.5rem; }
.w-12 { width: 3rem; }
.h-12 { height: 3rem; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }
.w-20 { width: 5rem; }
.h-20 { height: 5rem; }
.w-64 { width: 16rem; }
.w-80 { width: 20rem; }
.w-full { width: 100%; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-12 { padding: 3rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }

.m-0 { margin: 0; }
.m-4 { margin: 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }

.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-yellow-50 { background-color: #fffbeb; }
.bg-red-50 { background-color: #fef2f2; }

/* Couleurs personnalisées GoldSentinel */
.bg-gold { background-color: var(--gold); }
.bg-night-blue { background-color: var(--night-blue); }
.bg-forest-green { background-color: var(--forest-green); }
.bg-alert-red { background-color: var(--alert-red); }
.bg-pure-white { background-color: var(--pure-white); }

.text-gold { color: var(--gold); }
.text-night-blue { color: var(--night-blue); }
.text-forest-green { color: var(--forest-green); }
.text-alert-red { color: var(--alert-red); }
.text-pure-white { color: var(--pure-white); }

.border-gold { border-color: var(--gold); }
.border-night-blue { border-color: var(--night-blue); }
.border-night-blue-light { border-color: var(--night-blue-light); }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-6xl { font-size: 3.75rem; }

.text-white { color: white; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-blue-600 { color: #2563eb; }
.text-green-600 { color: #16a34a; }
.text-yellow-600 { color: #ca8a04; }
.text-yellow-800 { color: #92400e; }
.text-red-800 { color: #991b1b; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-t { border-top-width: 1px; }
.border-r-4 { border-right-width: 4px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }

.sticky { position: sticky; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

.flex-1 { flex: 1 1 0%; }
.flex-shrink-0 { flex-shrink: 0; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.transition-all { transition-property: all; }
.transition-colors { transition-property: color, background-color, border-color; }
.transition-transform { transition-property: transform; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }

.hover\\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\\:text-gray-900:hover { color: #111827; }
.hover\\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

.focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\\:ring-2:focus { box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1); }

.disabled\\:opacity-50:disabled { opacity: 0.5; }
.disabled\\:cursor-not-allowed:disabled { cursor: not-allowed; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.max-w-md { max-width: 28rem; }
.max-w-7xl { max-width: 80rem; }
.max-h-96 { max-height: 24rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-8 > * + * { margin-top: 2rem; }

@media (min-width: 640px) {
  .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\\:block { display: block; }
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\\:text-3xl { font-size: 1.875rem; }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:block { display: block; }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}
