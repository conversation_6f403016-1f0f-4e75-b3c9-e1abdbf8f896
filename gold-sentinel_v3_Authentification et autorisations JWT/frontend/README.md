<<<<<<< HEAD
# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript with type-aware lint rules enabled. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) for information on how to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.
=======
# GoldSentinel Frontend

Interface utilisateur React pour l'application GoldSentinel - Système de surveillance des activités minières.

## 🎨 Design System

### Couleurs principales
- **Or** (#FFD700) - Couleur principale, boutons primaires
- **Bleu nuit** (#0B1E3F) - Texte principal, navigation
- **Blanc pur** (#FFFFFF) - Arrière-plans
- **Vert forêt** (#1C6B48) - Actions positives, succès
- **Rouge alerte** (#E63946) - Alertes, erreurs

### Typographies
- **Titres** : Montserrat, Poppins
- **Texte courant** : Open Sans, Roboto
- **Taille minimale** : 14px

### Thème
- Ambiance sérieuse, institutionnelle et moderne
- Usage gouvernemental
- Interface responsive (desktop et tablette minimum)

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- npm ou yarn

### Installation
```bash
npm install
```

### Démarrage en développement
```bash
npm run dev
```

L'application sera accessible sur `http://localhost:5173`

### Build de production
```bash
npm run build
```

## 🏗️ Architecture

### Structure des dossiers
```
src/
├── components/
│   ├── common/          # Composants réutilisables
│   └── layout/          # Composants de mise en page
├── contexts/            # Contextes React (Auth, etc.)
├── pages/              # Pages de l'application
├── utils/              # Utilitaires
└── index.css           # Styles globaux
```

## 🔐 Système d'Authentification

### JWT Tokens
- **Access Token** : Authentification des requêtes API
- **Refresh Token** : Renouvellement automatique des tokens
- **Stockage** : localStorage (tokens + données utilisateur)

### Types d'utilisateurs
1. **Administrateur** - Accès complet au système
2. **Responsable Régional** - Gestion régionale, équipes, rapports
3. **Agent Analyste** - Analyses, détections, investigations
4. **Agent Technique** - Gestion technique, configurations, logs
5. **Agent Terrain** - Interface mobile-friendly, rapports terrain

## 🌐 API Integration

### Configuration
- **Base URL** : `http://localhost:8000/api/v1`
- **Authentification** : Bearer Token (JWT)

### Comptes de démonstration
```
Admin : <EMAIL> / admin123
Responsable : <EMAIL> / resp123
Analyste : <EMAIL> / analyst123
Technique : <EMAIL> / tech123
Terrain : <EMAIL> / field123
```

## 🎯 Fonctionnalités

### Pages disponibles
- **Dashboard** - Tableau de bord principal
- **Détections** - Gestion des détections
- **Analyses** - Lancement et suivi des analyses
- **Cartes** - Cartes interactives
- **Alertes** - Système d'alertes
- **Investigations** - Gestion des investigations
- **Rapports** - Génération de rapports
- **Statistiques** - Statistiques et métriques
- **Utilisateurs** - Gestion via Django Admin
- **Logs** - Journaux d'activité

## 📄 Licence

© 2024 GoldSentinel - Système de surveillance minière
>>>>>>> master
