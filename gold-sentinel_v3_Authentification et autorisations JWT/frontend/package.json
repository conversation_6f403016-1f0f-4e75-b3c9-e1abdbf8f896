{
<<<<<<< HEAD
  "name": "goldsentinel-react",
=======
  "name": "frontend",
>>>>>>> master
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint .",
    "preview": "vite preview"
  },
  "dependencies": {
<<<<<<< HEAD
    "react": "^19.1.0",
    "react-dom": "^19.1.0"
  },
  "devDependencies": {
    "@eslint/js": "^9.25.0",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "@vitejs/plugin-react": "^4.4.1",
=======
    "@heroicons/react": "^2.2.0",
    "axios": "^1.9.0",
    "date-fns": "^4.1.0",
    "lucide-react": "^0.511.0",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-router-dom": "^6.30.1",
    "recharts": "^2.15.3"
  },
  "devDependencies": {
    "@eslint/js": "^9.25.0",
    "@tailwindcss/forms": "^0.5.10",
    "@tailwindcss/typography": "^0.5.16",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "@vitejs/plugin-react": "^4.4.1",
    "autoprefixer": "^10.4.21",
>>>>>>> master
    "eslint": "^9.25.0",
    "eslint-plugin-react-hooks": "^5.2.0",
    "eslint-plugin-react-refresh": "^0.4.19",
    "globals": "^16.0.0",
<<<<<<< HEAD
=======
    "postcss": "^8.5.4",
    "tailwindcss": "^4.1.8",
>>>>>>> master
    "vite": "^6.3.5"
  }
}
