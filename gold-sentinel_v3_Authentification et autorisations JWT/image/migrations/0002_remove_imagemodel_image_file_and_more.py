# Generated by Django 5.2.1 on 2025-05-29 12:36

import django.contrib.gis.db.models.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0002_remove_usermodel_access_level_and_more'),
        ('image', '0001_initial'),
        ('region', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='imagemodel',
            name='image_file',
        ),
        migrations.RemoveField(
            model_name='imagemodel',
            name='thumbnail',
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='gee_asset_id',
            field=models.CharField(default='TEMP_ASSET_ID', help_text='ID asset Google Earth Engine', max_length=200, unique=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='gee_collection',
            field=models.Char<PERSON>ield(default='COPERNICUS/S2_SR', help_text='Collection GEE', max_length=100),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='ndti_data',
            field=models.JSONField(blank=True, help_text='Données NDTI calculées', null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='ndti_mean',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='ndvi_data',
            field=models.JSONField(blank=True, help_text='Données NDVI calculées', null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='ndvi_mean',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='ndwi_data',
            field=models.JSONField(blank=True, help_text='Données NDWI calculées', null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='ndwi_mean',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='processing_error',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='imagemodel',
            name='requested_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.usermodel'),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='center_lat',
            field=models.FloatField(default=8.0402),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='center_lon',
            field=models.FloatField(default=-2.8),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='cloud_coverage',
            field=models.FloatField(help_text='Pourcentage 0-100'),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='covered_area',
            field=django.contrib.gis.db.models.fields.PolygonField(blank=True, null=True, srid=4326),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='processing_status',
            field=models.CharField(choices=[('PENDING', 'En attente'), ('PROCESSING', 'Traitement en cours'), ('COMPLETED', 'Terminé'), ('ERROR', 'Erreur')], default='PENDING', max_length=20),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='resolution',
            field=models.FloatField(default=10, help_text='Résolution en mètres'),
        ),
        migrations.AlterField(
            model_name='imagemodel',
            name='satellite_source',
            field=models.CharField(choices=[('SENTINEL2', 'Sentinel-2'), ('LANDSAT8', 'Landsat-8'), ('LANDSAT9', 'Landsat-9')], max_length=50),
        ),
        migrations.AddIndex(
            model_name='imagemodel',
            index=models.Index(fields=['capture_date', 'processing_status'], name='satellite_i_capture_d2089d_idx'),
        ),
        migrations.AddIndex(
            model_name='imagemodel',
            index=models.Index(fields=['gee_asset_id'], name='satellite_i_gee_ass_ce18d6_idx'),
        ),
    ]
