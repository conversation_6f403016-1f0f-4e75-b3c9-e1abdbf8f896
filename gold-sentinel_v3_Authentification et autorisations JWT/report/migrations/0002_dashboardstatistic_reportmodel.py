# Generated by Django 5.2.1 on 2025-05-31 17:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('region', '0003_remove_regionmodel_geographic_zone'),
        ('report', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardStatistic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('statistic_type', models.CharField(choices=[('TOTAL_DETECTIONS', 'Total Detections'), ('ACTIVE_ALERTS', 'Active Alerts'), ('RESOLVED_ALERTS', 'Resolved Alerts'), ('NEW_MINING_SITES', 'New Mining Sites'), ('DEFORESTATION_HA', 'Total Deforestation (Ha)'), ('WATER_POLLUTION_INCIDENTS', 'Water Pollution Incidents'), ('VALIDATED_DETECTIONS', 'Validated Detections')], help_text='Type of aggregated statistic', max_length=50, unique=True)),
                ('value', models.FloatField(help_text='Aggregated value for the statistic')),
                ('date_calculated', models.DateField(help_text='Date for which this statistic was calculated or applies (e.g., end of month)')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dashboard_statistics', to='region.regionmodel')),
            ],
            options={
                'verbose_name': 'Dashboard Statistic',
                'verbose_name_plural': 'Dashboard Statistics',
                'db_table': 'dashboard_statistics',
                'ordering': ['statistic_type', '-date_calculated'],
                'indexes': [models.Index(fields=['statistic_type', 'date_calculated'], name='dashboard_s_statist_d3e603_idx')],
                'unique_together': {('statistic_type', 'region', 'date_calculated')},
            },
        ),
        migrations.CreateModel(
            name='ReportModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=150)),
                ('report_type', models.CharField(choices=[('SUMMARY', 'Summary Report'), ('REGION_DETAIL', 'Regional Detail Report'), ('ALERT_SUMMARY', 'Alerts Summary Report'), ('DEFORESTATION_TREND', 'Deforestation Trend Report'), ('WATER_QUALITY_TREND', 'Water Quality Trend Report')], help_text='Type of report generated', max_length=50)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('report_file', models.FileField(blank=True, help_text='Generated report file (e.g., PDF, CSV)', null=True, upload_to='reports/%Y/%m/')),
                ('external_url', models.URLField(blank=True, help_text='External URL if report is hosted elsewhere', max_length=500, null=True)),
                ('summary', models.TextField(blank=True, help_text='A brief summary or key findings of the report')),
                ('generated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_reports', to=settings.AUTH_USER_MODEL)),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reports', to='region.regionmodel')),
            ],
            options={
                'verbose_name': 'Report',
                'verbose_name_plural': 'Reports',
                'db_table': 'reports',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['report_type', 'region'], name='reports_report__ca59d7_idx'), models.Index(fields=['generated_by', 'created_at'], name='reports_generat_830760_idx')],
            },
        ),
    ]
